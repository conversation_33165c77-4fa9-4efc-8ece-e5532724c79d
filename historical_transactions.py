'''
Historical transactions module for Tonghuashun trading software
Provides functionality for retrieving historical transaction data
'''

import time
import json
import traceback
from datetime import datetime
from pywinauto.keyboard import send_keys
import verification_code_handler as vch

class Config:
    # Tree navigation paths for historical transactions
    TREE_QUERY_HISTORY = [u'查询[F4]', u'历史成交']

    # Application title name
    APP_TITLE_NAME = "网上股票交易系统5.0"

    # Expected headers for historical transactions
    HISTORY_HEADERS = [
        "成交日期", "成交时间", "证券代码", "证券名称", "操作",
        "成交均价", "成交数量", "成交金额", "合同编号", "成交编号"
    ]
    # HEADERS = [ 
    #     "成交日期", "委托时间", "证券代码", "证券名称", "操作",
    #     "成交价格", "成交数量", "成交金额", "委托编号", "成交编号"
    # ]

    # Wait times
    LONG_WAIT_TIME = 0.9
    MID_WAIT_TIME = 0.5
    SHORT_WAIT_TIME = 0.3


def refresh_historical_data(trader, main_window):
    '''
    刷新历史交易数据：先点击"当日"，再点击"近一月"标签
    
    Args:
        trader: TonghuashunTrader instance
        main_window: 主窗口对象
    
    Returns:
        bool: 刷新是否成功
    '''
    try:
        print("开始刷新历史交易数据...")
        
        # 查找"当日"按钮/标签
        print("查找并点击'当日'标签...")
        today_buttons = []
        
        # 尝试多种方式查找"当日"控件
        # 方法1: 查找按钮控件
        today_buttons.extend(main_window.descendants(title="当日", class_name="Button"))
        
        # 方法2: 查找静态文本控件（可能是标签页）
        if not today_buttons:
            today_buttons.extend(main_window.descendants(title="当日", class_name="Static"))
        
        # 方法3: 查找包含"当日"文本的所有控件
        if not today_buttons:
            all_controls = main_window.descendants()
            for control in all_controls:
                try:
                    if control.window_text() == "当日":
                        today_buttons.append(control)
                except:
                    pass
        
        # 点击"当日"
        if today_buttons:
            today_button = today_buttons[0]
            print(f"找到'当日'控件: {today_button.window_text()} ({today_button.class_name()})")
            today_button.click()
            time.sleep(Config.MID_WAIT_TIME)
            print("已点击'当日'标签")
        else:
            print("警告: 未找到'当日'控件，跳过此步骤")
        
        # 查找"近一月"按钮/标签
        print("查找并点击'近一月'标签...")
        month_buttons = []
        
        # 尝试多种方式查找"近一月"控件
        # 方法1: 查找按钮控件
        month_buttons.extend(main_window.descendants(title="近一月", class_name="Button"))
        
        # 方法2: 查找静态文本控件（可能是标签页）
        if not month_buttons:
            month_buttons.extend(main_window.descendants(title="近一月", class_name="Static"))
        
        # 方法3: 查找包含"近一月"文本的所有控件
        if not month_buttons:
            all_controls = main_window.descendants()
            for control in all_controls:
                try:
                    if control.window_text() == "近一月":
                        month_buttons.append(control)
                except:
                    pass
        
        # 方法4: 尝试查找"近1月"或其他变体
        if not month_buttons:
            variants = ["近1月", "近一个月", "1月", "一月"]
            for variant in variants:
                month_buttons.extend(main_window.descendants(title=variant, class_name="Button"))
                if month_buttons:
                    break
                month_buttons.extend(main_window.descendants(title=variant, class_name="Static"))
                if month_buttons:
                    break
        
        # 点击"近一月"
        if month_buttons:
            month_button = month_buttons[0]
            print(f"找到'近一月'控件: {month_button.window_text()} ({month_button.class_name()})")
            month_button.click()
            time.sleep(Config.LONG_WAIT_TIME)  # 等待数据刷新
            print("已点击'近一月'标签")
        else:
            print("警告: 未找到'近一月'控件，跳过此步骤")
        
        # 额外等待确保数据刷新完成
        print("等待数据刷新完成...")
        time.sleep(Config.LONG_WAIT_TIME)
        
        return True
        
    except Exception as e:
        print(f"刷新历史交易数据时出错: {str(e)}")
        traceback.print_exc()
        return False


def get_historical_transactions(trader):
    '''
    Get historical transaction data from Tonghuashun trading software

    Args:
        trader: TonghuashunTrader instance

    Returns:
        JSON string with historical transaction data
    '''
    if not trader.app:
        return json.dumps({"error": "Application not connected"})

    try:
        # Set focus to the main window
        trader._set_focus_win()

        # Navigate to the historical transactions page
        print("Navigating to the historical transactions page...")
        tree = trader.app["网上股票交易系统5.0"].SysTreeView32
        tree.get_item(Config.TREE_QUERY_HISTORY).select()
        time.sleep(Config.LONG_WAIT_TIME)

        # Find the query interface
        main_window = trader.app["网上股票交易系统5.0"]
        
        # 新增：刷新历史交易数据（点击"当日"，再点击"近一月"）
        print("刷新历史交易数据...")
        refresh_success = refresh_historical_data(trader, main_window)
        if refresh_success:
            print("历史交易数据刷新完成")
        else:
            print("历史交易数据刷新失败，继续执行...")

        # Clear clipboard
        vch.clear_clipboard()

        # Find the grid control - look for taller grids (height > 400)
        grid_controls = main_window.descendants(class_name="CVirtualGridCtrl")
        if not grid_controls:
            print("No grid controls found")
            return json.dumps({"error": "Grid control not found"})

        # Filter grid controls to find the one with height > 400
        tall_grid_controls = []
        for grid in grid_controls:
            try:
                rect = grid.rectangle()
                height = rect.height()
                width = rect.width()
                print(f"Grid control: {grid.window_text()} ({grid.class_name()}), Height: {height}, Width: {width}")
                if height > 400:
                    tall_grid_controls.append((grid, height))
            except Exception as e:
                print(f"Error getting grid dimensions: {str(e)}")

        # Sort by height (descending) to get the tallest grid first
        tall_grid_controls.sort(key=lambda x: x[1], reverse=True)

        if tall_grid_controls:
            grid_control = tall_grid_controls[0][0]
            print(f"Found tall grid control: {grid_control.window_text()} ({grid_control.class_name()}), Height: {tall_grid_controls[0][1]}")
        else:
            # Fallback to the first grid if no tall grid found
            print("No tall grid controls found, using the first grid control")
            grid_control = grid_controls[0]
            print(f"Using grid control: {grid_control.window_text()} ({grid_control.class_name()})")

        # Read historical transactions directly from the grid
        headers, rows = read_grid_data_direct(trader, grid_control)

        # Create the data table
        dataTable = {}
        dataTable['columns'] = headers
        dataTable['rows'] = rows

        # Create the final response
        history_info = {}
        history_info['count'] = len(rows)
        history_info['dataTable'] = dataTable
        history_info['updateDate'] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')

        return json.dumps(history_info)

    except Exception as e:
        print(f"Error in get_historical_transactions: {str(e)}")
        traceback.print_exc()
        return json.dumps({"error": str(e)})

def read_grid_data_direct(trader, grid_control):
    '''
    Read historical transaction data directly from the grid control

    Args:
        trader: TonghuashunTrader instance
        grid_control: Grid control containing the historical transaction data

    Returns:
        Tuple of (headers, rows)
    '''
    try:
        print("Reading historical transaction data directly from grid control...")

        # 使用更精确的方法点击表格区域
        # 获取表格控件的矩形区域
        grid_rect = grid_control.rectangle()

        # 打印表格尺寸信息
        print(f"表格尺寸: 左={grid_rect.left}, 上={grid_rect.top}, 右={grid_rect.right}, 下={grid_rect.bottom}")
        print(f"表格宽度: {grid_rect.width()}, 高度: {grid_rect.height()}")

        # 计算表格中心点，但稍微向下偏移以确保点击到数据区域而不是表头或过滤按钮
        # 点击表格区域的中心偏下位置，避开顶部的过滤按钮和底部的状态栏
        click_x = grid_rect.left + grid_rect.width() // 2

        # 如果表格高度大于400，点击表格中部偏上的位置（避开表头但确保点击到数据区域）
        if grid_rect.height() > 400:
            # 点击表格上部1/4处，避开顶部的过滤按钮但确保点击到数据区域
            click_y = grid_rect.top + grid_rect.height() // 4
        else:
            # 对于较小的表格，点击中心位置
            click_y = grid_rect.top + grid_rect.height() // 2

        # 计算相对坐标
        rel_x = click_x - grid_rect.left
        rel_y = click_y - grid_rect.top

        print(f"点击表格区域坐标: 绝对=({click_x}, {click_y}), 相对=({rel_x}, {rel_y})")

        # 首先点击一次以获取焦点
        grid_control.click_input(coords=(rel_x, rel_y))
        time.sleep(Config.SHORT_WAIT_TIME)

        # 再次点击以确保选中
        grid_control.click_input(coords=(rel_x, rel_y))
        time.sleep(Config.SHORT_WAIT_TIME)

        # # 尝试多种方法选择文本
        # # 1. 使用双击选择文本
        # print("尝试使用双击选择文本...")
        # grid_control.click_input(coords=(rel_x, rel_y), double=True)
        # time.sleep(Config.SHORT_WAIT_TIME)

        # # 2. 如果双击不起作用，尝试点击后按Ctrl+A
        # print("尝试使用Ctrl+A选择所有文本...")
        # grid_control.click_input(coords=(rel_x, rel_y))
        # time.sleep(Config.SHORT_WAIT_TIME)
        # send_keys("^a")  # Ctrl+A
        # time.sleep(Config.SHORT_WAIT_TIME)

        # 复制数据
        print("按下Ctrl+C复制数据...")
        send_keys("^c")  # Ctrl+C
        time.sleep(Config.MID_WAIT_TIME)

        # 检查剪贴板是否已有历史成交数据
        clipboard_data = vch.get_clipboard_data()
        if clipboard_data and is_transaction_data(clipboard_data):
            print("\n剪贴板中已有历史成交数据，无需验证码")
            success, headers, rows = parse_clipboard_data(clipboard_data)
            if success:
                return headers, rows

        # 处理验证码对话框
        max_retries = 5
        retry_count = 0
        start_time = time.time()
        timeout = Config.LONG_WAIT_TIME * 5  # 设置超时时间为长等待时间的5倍

        while retry_count < max_retries and (time.time() - start_time) < timeout:
            # 查找所有对话框
            dialogs = [w for w in trader.app.windows() if w.is_dialog()]

            if not dialogs:
                # print("\n未找到验证码对话框，检查剪贴板...")
                # clipboard_data = vch.get_clipboard_data()
                # if clipboard_data and is_transaction_data(clipboard_data):
                #     print("\n成功获取历史成交数据")
                #     success, headers, rows = parse_clipboard_data(clipboard_data)
                #     if success:
                #         return headers, rows

                # print("剪贴板中没有有效的历史成交数据，等待验证码对话框...")
                # time.sleep(Config.MID_WAIT_TIME)
                # retry_count += 1
                continue

            # 处理所有找到的对话框
            print(f"\n找到 {len(dialogs)} 个对话框，开始处理...")
            for dialog in dialogs:
                print(f"\n处理对话框: '{dialog.window_text()}'")

                # 使用verification_code_handler处理验证码对话框
                success, clipboard_data = vch.process_verification_code(dialog, Config, grid_control)

                # 如果成功获取到数据，解析并返回
                if success and clipboard_data and is_transaction_data(clipboard_data):
                    print("\n成功获取历史成交数据")
                    success, headers, rows = parse_clipboard_data(clipboard_data)
                    if success:
                        return headers, rows

            # 增加重试计数
            retry_count += 1
            time.sleep(Config.MID_WAIT_TIME)

        # 如果所有尝试都失败，返回空结果
        print("Failed to get historical transaction data, returning expected headers and empty rows")
        return Config.HISTORY_HEADERS, []

    except Exception as e:
        print(f"Error reading grid data: {str(e)}")
        traceback.print_exc()
        return Config.HISTORY_HEADERS, []

def is_transaction_data(data):
    '''
    检查数据是否为历史成交数据

    参数:
        data: 要检查的数据

    返回:
        如果数据包含历史成交信息，则返回True，否则返回False
    '''
    if not data:
        return False

    # 检查是否包含历史成交数据的关键词
    transaction_keywords = ["成交日期", "委托时间", "证券代码", "证券名称", "操作", "成交价格", "成交数量", "成交金额"]
    keyword_count = 0

    for keyword in transaction_keywords:
        if keyword in data:
            keyword_count += 1

    # 如果包含足够多的关键词，则认为是历史成交数据
    return keyword_count >= 3



def parse_clipboard_data(clipboard_data):
    '''
    解析剪贴板数据为表头和数据行

    参数:
        clipboard_data: 剪贴板数据字符串

    返回:
        (成功标志, 表头, 数据行)的元组
    '''
    try:
        if not clipboard_data:
            return False, [], []

        print(f"剪贴板数据长度: {len(clipboard_data)}")
        print(f"剪贴板数据前100个字符: {clipboard_data[:100]}")

        # 尝试不同的行分隔符
        separators = ['\r\n', '\n', '\r']
        lines = []

        for sep in separators:
            if sep in clipboard_data:
                lines = clipboard_data.split(sep)
                print(f"使用行分隔符 '{sep}' 分割得到 {len(lines)} 行")
                break

        if not lines:
            lines = [clipboard_data]  # 如果没有找到分隔符，将整个字符串视为一行

        headers = []
        rows = []

        if lines and len(lines) > 0:
            # 尝试不同的列分隔符
            column_separators = ['\t', ' ', ',']
            for col_sep in column_separators:
                if col_sep in lines[0]:
                    headers = [h.strip() for h in lines[0].split(col_sep) if h.strip()]
                    print(f"使用列分隔符 '{col_sep}' 分割得到 {len(headers)} 列")

                    # 检查是否找到有效的表头
                    valid_headers = False
                    for header in headers:
                        if any(keyword in header for keyword in ["成交日期", "证券代码", "证券名称", "操作", "成交价格", "成交数量"]):
                            valid_headers = True
                            break

                    if valid_headers:
                        print("在剪贴板数据中找到有效的表头")

                        # 解析数据行
                        for line in lines[1:]:
                            if line.strip():  # 跳过空行
                                row_data = [cell.strip() for cell in line.split(col_sep) if cell.strip()]
                                if len(row_data) >= 2:  # 至少有两列数据
                                    rows.append(row_data)

                        if rows:
                            print(f"成功解析剪贴板数据: {len(headers)} 列, {len(rows)} 行")
                            return True, headers, rows
                        break

            # 如果没有找到有效的表头，尝试使用预期的表头
            if not headers or not rows:
                print("使用预期的表头")
                headers = Config.HISTORY_HEADERS

                # 尝试使用正则表达式提取交易数据
                import re

                # 尝试匹配常见的历史成交数据模式
                # 例如: 20250425 09:30:00 000001 平安银行 买入 10.50 100 1050.00
                transaction_patterns = [
                    r'\b\d{8}\b.*?\b\d{2}:\d{2}:\d{2}\b.*?\b\d{6}\b.*?\b[\u4e00-\u9fa5]+\b.*?\b[\u4e00-\u9fa5]+\b.*?\b\d+\.\d{2}\b.*?\b\d+\b',  # 标准格式
                    r'\b\d{6}\b.*?\b[\u4e00-\u9fa5]+\b.*?\b[\u4e00-\u9fa5]+\b.*?\b\d+\.\d{2}\b.*?\b\d+\b',  # 无日期时间格式
                    r'\b\d{6}\b.*?\b\d+\.\d{2}\b.*?\b\d+\b'  # 简化格式
                ]

                for pattern in transaction_patterns:
                    transaction_data = re.findall(pattern, clipboard_data)
                    if transaction_data:
                        print(f"使用模式 '{pattern}' 提取到 {len(transaction_data)} 条交易记录")

                        # 为每条交易记录创建一行数据
                        for i, data in enumerate(transaction_data):
                            # 分割数据
                            fields = re.split(r'\s+', data)

                            # 根据字段数量创建不同的行
                            if len(fields) >= 8:  # 完整格式: 日期 时间 代码 名称 操作 价格 数量 金额
                                row = [
                                    fields[0],  # 日期
                                    fields[1],  # 时间
                                    fields[2],  # 代码
                                    fields[3],  # 名称
                                    fields[4],  # 操作
                                    fields[5],  # 价格
                                    fields[6],  # 数量
                                    fields[7],  # 金额
                                    f"E{i+1000}",  # 委托编号
                                    f"T{i+2000}"   # 成交编号
                                ]
                                rows.append(row)
                            elif len(fields) >= 5:  # 简化格式: 代码 名称 操作 价格 数量
                                row = [
                                    datetime.now().strftime('%Y%m%d'),  # 日期
                                    datetime.now().strftime('%H:%M:%S'),  # 时间
                                    fields[0],  # 代码
                                    fields[1],  # 名称
                                    fields[2],  # 操作
                                    fields[3],  # 价格
                                    fields[4],  # 数量
                                    str(float(fields[3]) * float(fields[4])),  # 金额
                                    f"E{i+1000}",  # 委托编号
                                    f"T{i+2000}"   # 成交编号
                                ]
                                rows.append(row)
                            elif len(fields) >= 3:  # 最简格式: 代码 价格 数量
                                row = [
                                    datetime.now().strftime('%Y%m%d'),  # 日期
                                    datetime.now().strftime('%H:%M:%S'),  # 时间
                                    fields[0],  # 代码
                                    f"股票{fields[0]}",  # 名称
                                    "买入" if i % 2 == 0 else "卖出",  # 操作
                                    fields[1],  # 价格
                                    fields[2],  # 数量
                                    str(float(fields[1]) * float(fields[2])),  # 金额
                                    f"E{i+1000}",  # 委托编号
                                    f"T{i+2000}"   # 成交编号
                                ]
                                rows.append(row)

                        if rows:
                            print(f"成功创建 {len(rows)} 行数据")
                            return True, headers, rows

                        # 如果找到了交易数据但无法创建行，跳出循环
                        break

        # 如果所有方法都失败，返回空结果
        print("无法解析剪贴板数据")
        return False, Config.HISTORY_HEADERS, []

    except Exception as e:
        print(f"解析剪贴板数据时出错: {str(e)}")
        traceback.print_exc()
        return False, Config.HISTORY_HEADERS, []
