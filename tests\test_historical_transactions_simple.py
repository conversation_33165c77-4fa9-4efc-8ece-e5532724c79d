'''
简单测试脚本，用于验证 historical_transactions.py 的功能
'''

import sys
import os
import json
import time
import traceback

# 添加父目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from trader import TonghuashunTrader
from historical_transactions import get_historical_transactions

def test_historical_transactions():
    '''测试获取历史成交数据'''
    print("开始测试获取历史成交数据...")

    # 创建交易者实例
    trader = TonghuashunTrader()

    # 连接到应用程序
    app_path = "C:/同花顺软件/同花顺/xiadan.exe"
    print(f"连接到应用程序: {app_path}")

    # 检查应用程序是否正在运行
    import psutil
    
    # 检查 xiadan.exe 是否正在运行
    xiadan_running = False
    for proc in psutil.process_iter(['pid', 'name']):
        if 'xiadan.exe' in proc.info['name'].lower():
            xiadan_running = True
            break
    
    if not xiadan_running:
        print("同花顺交易软件 (xiadan.exe) 未运行，请先启动软件")
        print("请手动启动同花顺交易软件，然后重新运行测试")
        return False

    # 尝试连接到应用程序
    try:
        if not trader.set_and_connect_app(app_path):
            print("连接到应用程序失败")
            return False
    except Exception as e:
        print(f"连接到应用程序时出错: {str(e)}")
        print("请确保同花顺交易软件已正确启动并登录")
        return False

    # 获取历史成交数据
    print("\n获取历史成交数据...")
    try:
        result = get_historical_transactions(trader)
        print(f"\n历史成交数据结果: {result}")
        
        # 解析结果
        data = json.loads(result)
        
        # 检查是否有错误
        if "error" in data:
            print(f"错误: {data['error']}")
            return False
        
        # 打印历史成交数据信息
        print(f"\n历史成交数据数量: {data.get('count', '未知')}")
        print(f"更新日期: {data.get('updateDate', '未知')}")
        
        if "dataTable" in data:
            dataTable = data["dataTable"]
            print(f"\n列: {dataTable.get('columns', [])}")
            
            rows = dataTable.get('rows', [])
            print(f"找到 {len(rows)} 条历史成交记录")
            
            # 打印前5条成交记录
            for i, row in enumerate(rows[:5]):
                print(f"成交记录 {i+1}: {row}")
            
            if len(rows) > 5:
                print("...")
        else:
            print("结果中没有 dataTable")
        
        return True
    except Exception as e:
        print(f"获取历史成交数据时出错: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = test_historical_transactions()
        print(f"\n测试{'成功' if success else '失败'}")
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        traceback.print_exc()
