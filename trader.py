'''
Tonghuashun stock trading operations module
Provides functionality for:
1. Getting account positions
2. Placing buy and sell orders
'''

import pywinauto
import time
import json
import os
import traceback
from datetime import datetime
from pywinauto.keyboard import send_keys

# Import verification code handler
from verification_code_handler import get_stock_positions

# Try to import win32api and win32con, or use alternative methods
try:
    import win32api
    import win32con
    HAS_WIN32 = True
except ImportError:
    print("win32api not available, using alternative methods for desktop path")
    HAS_WIN32 = False

# Configuration class
class Config:
    # Wait times
    LONG_WAIT_TIME = 0.9
    MID_WAIT_TIME = 0.5
    SHORT_WAIT_TIME = 0.3

    # Application window titles
    APP_TITLE_NAME = u'网上股票交易系统5.0'
    POP_DLG_NAME = u'Dialog'

    # Tree navigation paths
    TREE_QUERY_STOCK = [u'查询[F4]', u'资金股票']
    TREE_BUY = [u'买入[F1]']
    TREE_SELL = [u'卖出[F2]']

    # Button names
    DLG_BUY_BUTTON = u'买入'
    DLG_SELL_BUTTON = u'卖出'
    BUTTON_YES_Y = u'是(Y)'
    BUTTON_CONFIRM = u'确定'

    # Dialog titles
    COMMISSION_CONFIRMATION = u'委托确认'

    # Keyboard shortcuts
    ENTER = '{ENTER}'
    ESC = '{ESC}'
    F1 = '{F1}'  # Buy
    F2 = '{F2}'  # Sell
    F4 = '{F4}'  # Query
    F5 = '{F5}'  # 刷新

    # Account information labels
    MONEY_OVERAGE = u'资金余额'
    MONEY_FREEZE = u'冻结金额'
    MONEY_CAN_USE = u'可用金额'
    MONEY_CAN_TAKE_OUT = u'可取金额'
    STOCK_MARKET_VALUE = u'股票市值'
    TOTAL_ASSETS = u'总 资 产'
    CASH_ASSETS = u'资金资产'
    RMB = u'人民币'

    # Server port
    PORT = 8888

# Stock trading handler class
class TonghuashunTrader:
    def __init__(self):
        self.app = None
        self.app_path = None

    def set_and_connect_app(self, app_path):
        '''Connect to the Tonghuashun application'''
        self.app_path = app_path
        try:
            self.app = pywinauto.Application(backend="win32").connect(path=self.app_path, timeout=10)
            return True
        except Exception as e:
            print(f"Error connecting to application: {str(e)}")
            return False

    def _set_focus_win(self):
        '''Set focus to the main application window'''
        if not self.app:
            return False

        w = self.app.top_window()

        try:
            # Bring window into foreground
            w.set_focus()
        except Exception as e:
            print(f"Warning: Could not set focus to window: {str(e)}")

        return True

    def _find_control_by_label(self, window, label_text):
        """Find a control (typically an Edit control) that is adjacent to a label with the given text"""
        # First, find all Static controls (labels) with the given text
        labels = window.children(class_name="Static", title=label_text)
        if not labels:
            print(f"Label '{label_text}' not found")
            return None

        label = labels[0]
        print(f"Found label '{label_text}' at {label.rectangle()}")

        # Find all Edit controls
        edit_controls = window.children(class_name="Edit")
        if not edit_controls:
            print("No Edit controls found")
            return None

        # Find the Edit control that is closest to the label (typically to the right or below)
        label_rect = label.rectangle()
        closest_edit = None
        min_distance = float('inf')

        for edit in edit_controls:
            edit_rect = edit.rectangle()

            # Calculate distance (simple horizontal distance if the controls are roughly aligned)
            if abs(edit_rect.top - label_rect.top) < 30:  # Roughly aligned horizontally
                distance = edit_rect.left - label_rect.right
                if distance > 0 and distance < min_distance:  # Edit is to the right of the label
                    min_distance = distance
                    closest_edit = edit

        if closest_edit:
            print(f"Found closest Edit control at {closest_edit.rectangle()}")
            return closest_edit
        else:
            print(f"No Edit control found near label '{label_text}'")
            return None

    def _find_trade_interface(self, main_window, trade_type):
        """Find the buy or sell interface dialog within the main window

        Args:
            main_window: The main application window
            trade_type: 'BUY' or 'SELL'

        Returns:
            The dialog window if found, None otherwise
        """
        # Set up parameters based on trade type
        if trade_type.upper() == 'BUY':
            button_title = Config.DLG_BUY_BUTTON
            price_label = "买入价格"
            dialog_keyword = "买入"
        else:  # SELL
            button_title = Config.DLG_SELL_BUTTON
            price_label = "卖出价格"
            dialog_keyword = "卖出"

        # Look for dialogs that might be the trade interface
        dialogs = main_window.children(class_name="#32770")

        for dialog in dialogs:
            # Check if this dialog has the button and related labels
            trade_buttons = dialog.children(title=button_title, class_name="Button")
            trade_labels = dialog.children(class_name="Static", title=price_label)

            if trade_buttons and trade_labels:
                print(f"Found {trade_type.lower()} interface dialog: {dialog.window_text()}")
                return dialog

        # If not found within child dialogs, try to find it by looking at all windows
        trade_dialogs = [w for w in self.app.windows() if dialog_keyword in w.window_text()]
        if trade_dialogs:
            print(f"Found {trade_type.lower()} dialog by title: {trade_dialogs[0].window_text()}")
            return trade_dialogs[0]

        # If still not found, look for any dialog that appeared after pressing F1/F2
        dialogs = [w for w in self.app.windows() if w.class_name() == "#32770" and w.is_visible()]
        for dialog in dialogs:
            print(f"Found dialog after pressing {trade_type}: {dialog.window_text()}")
            # Check if this dialog has trade-related controls
            trade_labels = dialog.children(class_name="Static", title=price_label)
            if trade_labels:
                print(f"This appears to be the {trade_type.lower()} interface: {dialog.window_text()}")
                return dialog

        print(f"Could not find {trade_type.lower()} interface dialog")
        return None


    def _ocr_recognize_dialog_content(self, dialog):
        '''Use OCR to recognize dialog content'''
        try:
            # Get all static text from the dialog
            static_texts = dialog.children(class_name="Static")
            dialog_text = ""

            for static in static_texts:
                try:
                    text = static.window_text()
                    if text and not text.isspace():
                        dialog_text += text + "\n"
                except Exception as e:
                    print(f"Error getting text from static control: {str(e)}")

            return dialog_text.strip()
        except Exception as e:
            print(f"Error recognizing dialog content: {str(e)}")
            traceback.print_exc()
            return ""

    def _handle_popup_dialogs(self, timeout=10):
        """Handle any popup dialogs that might appear after an operation

        This method handles two types of dialogs:
        1. Confirmation dialog - Contains text like "您是否确定以上买入委托？" (Are you sure to confirm this buy order?)
        2. Result dialog - Contains text like "委托已成功提交" (Order submitted successfully) or error messages

        Returns:
            A string containing the dialog messages
        """
        start_time = time.time()
        result_message = ""
        confirmation_handled = False
        result_handled = False

        while time.time() - start_time < timeout:
            # Look for any visible dialogs
            dialogs = [w for w in self.app.windows() if w.class_name() == "#32770" and w.is_visible()]
            if not dialogs:
                time.sleep(0.5)
                continue

            for dialog in dialogs:
                dialog_text = dialog.window_text()
                print(f"\nHandling popup dialog: {dialog_text}")

                # Get all text from the dialog
                dialog_content = self._ocr_recognize_dialog_content(dialog)
                print(f"Dialog content: {dialog_content}")

                # Determine dialog type
                is_confirmation_dialog = False
                is_result_dialog = False
                is_verification_dialog = False

                # Check if this is a verification code dialog
                if "\u63d0\u793a" in dialog_text:  # "提示"
                    # Check if it contains static controls with verification code text
                    static_texts = dialog.children(class_name="Static")
                    for static in static_texts:
                        try:
                            text = static.window_text()
                            if "\u9a8c\u8bc1\u7801" in text or "\u5148\u8f93\u5165\u9a8c\u8bc1\u7801" in text or "\u68c0\u6d4b\u5230" in text:
                                is_verification_dialog = True
                                print("*** Detected verification code dialog! ***")
                                break
                        except Exception as e:
                            pass

                # Check if this is a confirmation dialog
                if not is_verification_dialog:
                    confirmation_keywords = ["\u60a8\u662f\u5426\u786e\u5b9a", "\u786e\u8ba4\u59d4\u6258", "\u786e\u5b9a\u4ee5\u4e0a", "\u786e\u8ba4\u4ee5\u4e0a"]
                    for keyword in confirmation_keywords:
                        if keyword in dialog_content:
                            is_confirmation_dialog = True
                            print("*** Detected order confirmation dialog! ***")
                            break

                # Check if this is a result dialog
                result_keywords = ["\u59d4\u6258\u5df2\u6210\u529f\u63d0\u4ea4", "\u59d4\u6258\u5df2\u63d0\u4ea4", "\u4ea4\u6613\u6210\u529f", "\u6210\u529f\u63d0\u4ea4", "\u5931\u8d25", "\u9519\u8bef", "\u4e0d\u8db3", "\u7981\u6b62"]
                for keyword in result_keywords:
                    if keyword in dialog_content:
                        is_result_dialog = True
                        print("*** Detected order result dialog! ***")
                        break

                # Handle the dialog based on its type
                if is_verification_dialog:
                    # Handle verification code dialog
                    print("Handling verification code dialog...")
                    if dialog_content:
                        result_message += dialog_content + "\n"

                    # Check if the dialog still exists (if not, verification was successful)
                    try:
                        if not dialog.exists():
                            print("Verification code dialog no longer exists, verification successful")
                            continue
                    except Exception as e:
                        print(f"Error checking dialog existence: {str(e)}")
                        continue

                elif is_confirmation_dialog:
                    # Handle confirmation dialog
                    print("Handling order confirmation dialog...")
                    confirmation_handled = True

                    if dialog_content:
                        result_message += "\u786e\u8ba4\u5bf9\u8bdd\u6846: " + dialog_content + "\n"

                    # Look for "\u662f" (Yes) button to confirm the order
                    yes_buttons = dialog.children(title="\u662f", class_name="Button")
                    if yes_buttons:
                        print("Clicking '\u662f' button to confirm order...")
                        yes_buttons[0].click()
                        time.sleep(1)  # Wait a bit after clicking
                    else:
                        # Try alternative buttons
                        alt_buttons = ["\u662f(Y)", "\u786e\u5b9a", "\u786e\u8ba4"]
                        button_clicked = False
                        for button_text in alt_buttons:
                            buttons = dialog.children(title=button_text, class_name="Button")
                            if buttons:
                                print(f"Clicking '{button_text}' button to confirm order...")
                                buttons[0].click()
                                button_clicked = True
                                time.sleep(1)  # Wait a bit after clicking
                                break

                        if not button_clicked:
                            print("No confirmation button found, trying generic button...")
                            buttons = dialog.children(class_name="Button")
                            if buttons:
                                print(f"Clicking generic button: {buttons[0].window_text()}")
                                buttons[0].click()
                                time.sleep(1)  # Wait a bit after clicking

                elif is_result_dialog:
                    # Handle result dialog
                    print("Handling order result dialog...")
                    result_handled = True

                    if dialog_content:
                        result_message += "\u7ed3\u679c\u5bf9\u8bdd\u6846: " + dialog_content + "\n"

                    # Check for success or failure keywords
                    if "\u59d4\u6258\u5df2\u6210\u529f\u63d0\u4ea4" in dialog_content or "\u6210\u529f" in dialog_content:
                        print("Order submitted successfully!")
                        result_message += "\u4ea4\u6613\u72b6\u6001: \u6210\u529f\n"
                    elif "\u5931\u8d25" in dialog_content or "\u9519\u8bef" in dialog_content or "\u4e0d\u8db3" in dialog_content:
                        print("Order submission failed!")
                        result_message += "\u4ea4\u6613\u72b6\u6001: \u5931\u8d25\n"

                    # Click OK button to dismiss the dialog
                    ok_buttons = dialog.children(title="\u786e\u5b9a", class_name="Button")
                    if ok_buttons:
                        print("Clicking '\u786e\u5b9a' button to dismiss result dialog...")
                        ok_buttons[0].click()
                        time.sleep(1)  # Wait a bit after clicking
                    else:
                        # Try any button
                        buttons = dialog.children(class_name="Button")
                        if buttons:
                            print(f"Clicking generic button: {buttons[0].window_text()}")
                            buttons[0].click()
                            time.sleep(1)  # Wait a bit after clicking

                else:
                    # Handle other dialogs
                    print("Handling generic dialog...")

                    if dialog_content:
                        result_message += dialog_content + "\n"

                    # Look for common buttons to click (\u786e\u5b9a/OK, \u662f/Yes)
                    buttons_to_try = [
                        "\u786e\u5b9a",  # OK
                        "\u662f",    # Yes
                        "\u662f(Y)",  # Yes(Y)
                        "OK",
                        "\u786e\u8ba4"   # Confirm
                    ]

                    button_clicked = False
                    for button_text in buttons_to_try:
                        buttons = dialog.children(title=button_text, class_name="Button")
                        if buttons:
                            print(f"Clicking '{button_text}' button...")
                            buttons[0].click()
                            button_clicked = True
                            time.sleep(1)  # Wait a bit after clicking
                            break

                    if not button_clicked:
                        # If no specific button was found, try to find any button
                        buttons = dialog.children(class_name="Button")
                        if buttons:
                            print(f"Clicking generic button: {buttons[0].window_text()}")
                            buttons[0].click()
                            time.sleep(1)  # Wait a bit after clicking
                        else:
                            print("No buttons found in dialog, trying to close it...")
                            try:
                                # Try to close the dialog by sending Escape key
                                dialog.type_keys("{ESC}")
                                time.sleep(1)
                            except:
                                print("Failed to close dialog with ESC key")

            # Check if we still have visible dialogs
            dialogs = [w for w in self.app.windows() if w.class_name() == "#32770" and w.is_visible()]
            if not dialogs:
                print("All dialogs handled")
                # If we've handled both confirmation and result dialogs, or just the result dialog, we're done
                if result_handled or (confirmation_handled and not result_handled and time.time() - start_time >= 5):
                    return result_message.strip()
                # If we've only handled the confirmation dialog, wait a bit for the result dialog
                elif confirmation_handled and not result_handled:
                    time.sleep(1)  # Wait for result dialog to appear

        print(f"Timed out after {timeout} seconds waiting for dialogs to be handled")
        return result_message.strip()



    def _get_documents_path(self):
        '''Get the documents path'''
        if HAS_WIN32:
            key = win32api.RegOpenKey(win32con.HKEY_CURRENT_USER,
                                    r'Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders', 0,
                                    win32con.KEY_READ)
            return win32api.RegQueryValueEx(key, 'Documents')[0]
        else:
            # Alternative method using environment variables
            return os.path.join(os.path.expanduser('~'), 'Documents')

    def _find_account_info(self, query_dialog):
        '''Find and extract account information labels'''
        try:
            # Look for static text controls with account information
            account_info = {}

            # Define the labels we're looking for
            labels_to_find = [
                Config.MONEY_OVERAGE,      # 资金余额
                Config.MONEY_FREEZE,       # 冻结金额
                Config.MONEY_CAN_USE,      # 可用金额
                Config.MONEY_CAN_TAKE_OUT, # 可取金额
                Config.STOCK_MARKET_VALUE, # 股票市值
                Config.TOTAL_ASSETS        # 总资产
            ]

            # Find all static text controls
            static_controls = query_dialog.descendants(class_name="Static")
            print(f"Found {len(static_controls)} static controls in total")

            # First, collect all label-value pairs
            import re
            label_value_pairs = []

            for i in range(len(static_controls) - 1):
                try:
                    current = static_controls[i]
                    next_control = static_controls[i + 1]

                    current_text = current.window_text()
                    next_text = next_control.window_text()

                    if current_text and current_text.strip() and next_text and next_text.strip():
                        # Check if the next control might be a value (contains digits)
                        if re.match(r'^\d+(\.\d+)?$', next_text):
                            current_rect = current.rectangle()
                            next_rect = next_control.rectangle()

                            # Store the label-value pair
                            label_value_pairs.append({
                                "label": current_text,
                                "value": next_text,
                                "label_index": i,
                                "value_index": i + 1,
                                "label_rect": current_rect,
                                "value_rect": next_rect
                            })

                            print(f"Found label-value pair: '{current_text}' -> '{next_text}'")
                except Exception as e:
                    print(f"Error processing controls at index {i}: {str(e)}")

            # Now, find the best match for each label we're looking for
            for label_text in labels_to_find:
                # Find all pairs with this label
                matching_pairs = [pair for pair in label_value_pairs if label_text in pair["label"]]

                if not matching_pairs:
                    print(f"No matches found for label '{label_text}'")
                    account_info[label_text] = "0.00"  # Default value
                    continue

                print(f"Found {len(matching_pairs)} matches for label '{label_text}':")
                for i, pair in enumerate(matching_pairs):
                    print(f"  Match {i+1}: '{pair['label']}' -> '{pair['value']}'")

                # If we have multiple matches, use heuristics to select the best one
                if len(matching_pairs) == 1:
                    # Only one match, use it
                    account_info[label_text] = matching_pairs[0]["value"]
                    print(f"Selected value for {label_text}: {matching_pairs[0]['value']} (only match)")
                else:
                    # Multiple matches, use heuristics

                    # For 可用金额 (available funds), prefer the larger value
                    if label_text == Config.MONEY_CAN_USE:
                        # Convert to float for comparison
                        # values = [float(pair["value"]) for pair in matching_pairs]
                        # max_value = max(values)
                        # max_index = values.index(max_value)
                        # account_info[label_text] = matching_pairs[max_index]["value"]
                        account_info[label_text] = matching_pairs[-1]["value"]

                        print(f"Selected value for {label_text}: {matching_pairs[-1]['value']} (largest value)")

                    # For 总资产 (total assets), prefer the larger value
                    elif label_text == Config.TOTAL_ASSETS:
                        # Convert to float for comparison
                        # values = [float(pair["value"]) for pair in matching_pairs]
                        # max_value = max(values)
                        # max_index = values.index(max_value)
                        # account_info[label_text] = matching_pairs[max_index]["value"]

                        account_info[label_text] = matching_pairs[-1]["value"]
                        print(f"Selected value for {label_text}: {matching_pairs[-1]['value']} (largest value)")

                    # For other labels, prefer the first match
                    elif label_text == Config.MONEY_CAN_TAKE_OUT:
                        # Convert to float for comparison
                        # values = [float(pair["value"]) for pair in matching_pairs]
                        # max_value = max(values)
                        # max_index = values.index(max_value)
                        # account_info[label_text] = matching_pairs[max_index]["value"]

                        account_info[label_text] = matching_pairs[-1]["value"]
                        print(f"Selected value for {label_text}: {matching_pairs[-1]['value']} (largest value)")
                    else:
                        account_info[label_text] = matching_pairs[0]["value"]
                        print(f"Selected value for {label_text}: {matching_pairs[0]['value']} (first match)")

            # Special check for 可用金额 (available funds)
            if Config.MONEY_CAN_USE in account_info:
                print(f"Final 可用金额 (available funds): {account_info[Config.MONEY_CAN_USE]}")

                # If the value seems too small, look for a specific pattern that might indicate the correct value
                if float(account_info[Config.MONEY_CAN_USE]) < 100.0:
                    # Look for controls with numeric values that might be the correct available funds
                    for control in static_controls:
                        text = control.window_text()
                        if text and re.match(r'\d{4}\.\d{2}', text):  # Pattern like 3914.07
                            print(f"Found potential 可用金额 value: {text}")
                            # Verify this is a reasonable value (e.g., between 100 and 1,000,000)
                            value = float(text)
                            if 100.0 <= value <= 1000000.0:
                                account_info[Config.MONEY_CAN_USE] = text
                                print(f"Updated 可用金额 to: {text}")
                                break

            return account_info
        except Exception as e:
            print(f"Error finding account info: {str(e)}")
            traceback.print_exc()
            return {label: "0.00" for label in [
                Config.MONEY_OVERAGE, Config.MONEY_FREEZE, Config.MONEY_CAN_USE,
                Config.MONEY_CAN_TAKE_OUT, Config.STOCK_MARKET_VALUE, Config.TOTAL_ASSETS
            ]}

    def _read_grid_data_direct(self, grid_control):
        '''Read data directly from the grid control'''
        try:
            print("Reading data directly from grid control...")
            # We still need the grid_control parameter for backward compatibility
            # but we'll use the get_stock_positions function instead

            # Define the expected headers based on the example
            expected_headers = [
                "证券代码", "证券名称", "股票余额", "可用余额", "冻结数量",
                "成本价", "市价", "盈亏", "盈亏比(%)", "市值",
                "当日买入", "当日卖出", "交易市场"
            ]

            # 使用验证码处理模块获取股票持仓数据
            success, headers, rows = get_stock_positions(self, Config)
            if success:
                print(f"Successfully read data using verification_code_handler: {len(headers)} columns, {len(rows)} rows")
                return headers, rows

            # 如果获取失败，返回预期的标题和空行
            print("Failed to get stock positions, returning expected headers and empty rows")
            return expected_headers, []

        except Exception as e:
            print(f"Error reading grid data: {str(e)}")
            traceback.print_exc()
            return [], []

    def get_positions(self):
        '''Get account positions and balance information by directly reading from the interface'''
        if not self.app:
            return json.dumps({"error": "Application not connected"})

        try:
            # Set focus to the main window
            self._set_focus_win()

            # Navigate to the stock query page
            print("Navigating to the stock query page...")
            tree = self.app[Config.APP_TITLE_NAME].SysTreeView32
            tree.get_item(Config.TREE_QUERY_STOCK).select()
            time.sleep(Config.LONG_WAIT_TIME)

            # Find the query interface
            main_window = self.app[Config.APP_TITLE_NAME]

            # F5
            print("Pressing F5 to refresh the page...")
            send_keys(Config.F5)
            time.sleep(Config.MID_WAIT_TIME)

            # Get account balance information
            print("\nGetting account balance information...")
            account_info = self._find_account_info(main_window)

            # Get stock positions by directly reading from the grid
            print("\nGetting stock positions by directly reading from the grid...")

            # Find the grid control
            grid_controls = main_window.descendants(class_name="CVirtualGridCtrl")
            if not grid_controls:
                print("No grid controls found")
                return json.dumps({"error": "Grid control not found"})

            grid_control = grid_controls[0]
            print(f"Found grid control: {grid_control.window_text()} ({grid_control.class_name()})")

            # Read positions directly from the grid
            headers, rows = self._read_grid_data_direct(grid_control)

            # Create the data table
            dataTable = {}
            dataTable['columns'] = headers
            dataTable['rows'] = rows

            # Create the final response
            subAccounts = {}
            subAccounts[Config.RMB] = account_info

            fund_info = {}
            fund_info['subAccounts'] = subAccounts
            fund_info['count'] = len(rows)
            fund_info['dataTable'] = dataTable
            fund_info['updateDate'] = datetime.now().strftime('%Y-%m-%dT%H:%M:%S')

            return json.dumps(fund_info)

        except Exception as e:
            print(f"Error in get_positions: {str(e)}")
            traceback.print_exc()
            return json.dumps({"error": str(e)})

    def place_order(self, order_dict):
        '''Place a buy or sell order'''
        if not self.app:
            return json.dumps({"result": False, "info": "Application not connected"})

        try:
            # Set focus to the main window
            self._set_focus_win()

            # Navigate to the buy or sell page
            main_window = self.app[Config.APP_TITLE_NAME]

            trade_type = order_dict['action'].upper()
            if trade_type == 'BUY':
                # Navigate to buy interface using F1 key
                print("Pressing F1 to navigate to buy interface...")
                send_keys(Config.F1)
                time.sleep(Config.MID_WAIT_TIME)

                # Find the buy interface dialog
                trade_dialog = self._find_trade_interface(main_window, 'BUY')
                if not trade_dialog:
                    return json.dumps({"result": False, "info": "Could not find buy interface dialog"})

                # Find the stock code, price, and quantity fields
                stock_code_edit = self._find_control_by_label(trade_dialog, "证券代码")
                price_edit = self._find_control_by_label(trade_dialog, "买入价格")
                quantity_edit = self._find_control_by_label(trade_dialog, "买入数量")

                # Check if we found all the necessary controls
                if not (stock_code_edit and price_edit and quantity_edit):
                    return json.dumps({"result": False, "info": "Could not find all necessary controls for buying"})

                # Find and click the buy button
                trade_buttons = trade_dialog.children(title=Config.DLG_BUY_BUTTON, class_name="Button")
                if not trade_buttons:
                    return json.dumps({"result": False, "info": "Buy button not found"})

                trade_button = trade_buttons[0]

            else:  # SELL
                # Navigate to sell interface using F2 key
                print("Pressing F2 to navigate to sell interface...")
                send_keys(Config.F2)
                time.sleep(Config.MID_WAIT_TIME)

                # Find the sell interface dialog
                trade_dialog = self._find_trade_interface(main_window, 'SELL')
                if not trade_dialog:
                    return json.dumps({"result": False, "info": "Could not find sell interface dialog"})

                # Find the stock code, price, and quantity fields
                stock_code_edit = self._find_control_by_label(trade_dialog, "证券代码")
                price_edit = self._find_control_by_label(trade_dialog, "卖出价格")
                quantity_edit = self._find_control_by_label(trade_dialog, "卖出数量")

                # Check if we found all the necessary controls
                if not (stock_code_edit and price_edit and quantity_edit):
                    return json.dumps({"result": False, "info": "Could not find all necessary controls for selling"})

                # Find and click the sell button
                trade_buttons = trade_dialog.children(title=Config.DLG_SELL_BUTTON, class_name="Button")
                if not trade_buttons:
                    return json.dumps({"result": False, "info": "Sell button not found"})

                trade_button = trade_buttons[0]

            # Enter stock code - use double-click to select all text
            stock_code_edit.set_focus()
            time.sleep(Config.SHORT_WAIT_TIME)
            # Double-click to select all text
            stock_code_edit.click_input(double=True)
            time.sleep(Config.SHORT_WAIT_TIME)
            send_keys("{BACKSPACE}")  # Clear the field
            send_keys(order_dict['symbol'])
            time.sleep(Config.SHORT_WAIT_TIME)

            # Enter price - clear existing price first using double-click
            price_edit.set_focus()
            time.sleep(Config.SHORT_WAIT_TIME)
            # Double-click to select all text
            price_edit.click_input(double=True)
            time.sleep(Config.SHORT_WAIT_TIME)
            send_keys("{BACKSPACE}")  # Clear the field
            time.sleep(Config.SHORT_WAIT_TIME)  # Wait a bit after clearing

            # Check if the field is empty, if not try again
            current_text = price_edit.window_text()
            if current_text and current_text.strip():
                print(f"Field not cleared, current text: '{current_text}'. Trying again...")
                price_edit.set_focus()
                time.sleep(Config.SHORT_WAIT_TIME)
                # Try triple-click as an alternative
                price_edit.click_input(button='left', coords=(5, 5))
                time.sleep(Config.SHORT_WAIT_TIME)
                price_edit.click_input(button='left', coords=(5, 5))
                time.sleep(Config.SHORT_WAIT_TIME)
                price_edit.click_input(button='left', coords=(5, 5))
                time.sleep(Config.SHORT_WAIT_TIME)
                send_keys("{BACKSPACE}")  # Clear the field
                time.sleep(Config.SHORT_WAIT_TIME)

            # Now enter the new price
            price_edit.set_focus()
            send_keys(str(order_dict['price']))
            time.sleep(Config.SHORT_WAIT_TIME)

            # Enter quantity - use double-click to select all text
            quantity_edit.set_focus()
            # time.sleep(Config.SHORT_WAIT_TIME)
            # Double-click to select all text
            # quantity_edit.click_input(double=True)
            time.sleep(Config.SHORT_WAIT_TIME)
            send_keys("{BACKSPACE}")  # Clear the field
            send_keys(str(order_dict['amount']))
            time.sleep(Config.SHORT_WAIT_TIME)

            # Check if the button is enabled
            if not trade_button.is_enabled():
                return json.dumps({"result": False, "info": f"{trade_type} button is disabled"})

            # Click the button
            try:
                trade_button.click()
                time.sleep(Config.MID_WAIT_TIME)
            except Exception as e:
                print(f"Error clicking {trade_type.lower()} button: {str(e)}")
                # Try alternative method
                try:
                    trade_button.click_input()
                    time.sleep(Config.MID_WAIT_TIME)
                except Exception as e2:
                    print(f"Alternative click method also failed: {str(e2)}")
                    return json.dumps({"result": False, "info": f"Could not click {trade_type.lower()} button: {str(e)}"})

            # Handle the confirmation dialog
            result = {"result": True, "info": "Order submitted successfully"}
            result_message = self._handle_popup_dialogs(timeout=10)
            if result_message:
                result["message"] = result_message
                # Check for specific error conditions
                if '可用资金不足' in result_message:
                    result["info"] = "Insufficient funds"
                    result["result"] = False
                elif '可用数量不足' in result_message:
                    result["info"] = "Insufficient shares"
                    result["result"] = False
                elif '请输入委托数量' in result_message:
                    result["info"] = "Please enter order quantity"
                    result["result"] = False
                elif '当前时间不允许' in result_message:
                    result["info"] = "Trading not allowed at current time"
                    result["result"] = False
                elif '委托已成功提交' in result_message:
                    result["info"] = "Order submitted successfully"
                    result["result"] = True
            return json.dumps(result)

        except Exception as e:
            print(f"Error in place_order: {str(e)}")
            traceback.print_exc()
            return json.dumps({"result": False, "info": str(e)})
